/**
 * Mock AI 服务 - 用于开发和测试
 */

import { AIRequest, AIStreamCallback, AIService } from './aiService';

/**
 * Mock 响应数据
 */
const MOCK_RESPONSES: Record<string, (text: string) => string> = {
  'summary': (text: string) => `已深度思考（用时3秒）^

嗯，用户要求我作为专业改写专家，将一段中文内容改写更正式官方。让我看看原文："${text.slice(0, 50)}${text.length > 50 ? '...' : ''}"。

这句子很有诗意韵味。"${text.slice(0, 20)}"是南京的历史名巷，"王羲之"是书圣，"墨韵风骨"体现了书法的神韵。

总结：${text}

这段文字体现了深厚的文化底蕴，融合了历史、艺术和文学的多重内涵。`,

  'translate': (text: string) => `Translation of "${text.slice(0, 30)}${text.length > 30 ? '...' : ''}":

Original text: ${text}

English translation: This text appears to be in Chinese and discusses historical and cultural elements. The translation maintains the poetic and cultural nuances of the original text, preserving its artistic and literary value.

Translation notes: The content reflects deep cultural heritage and should be understood within its historical context.`,

  'abbreviate': (text: string) => {
    const abbreviated = text.split('').slice(0, Math.floor(text.length * 0.6)).join('');
    return `原文缩写版本：

${abbreviated}

缩写说明：保留了原文的核心信息，去除了冗余表达，使内容更加简洁明了。

已生成内容 ${abbreviated.length} 字`;
  },

  'expand': (text: string) => `扩写内容：

${text}

进一步阐述：这段文字蕴含着深厚的文化底蕴，体现了中华文明的博大精深。从历史的角度来看，这样的表达方式承载着丰富的文化内涵，值得我们深入思考和品味。

文化背景：这类表达方式在中国传统文学中具有重要地位，它不仅传达了字面意思，更承载了深层的文化象征意义。通过这种方式，我们可以更好地理解和传承中华文化的精髓。

已生成内容 ${text.length + 200} 字`,

  'polish': (text: string) => `润色后的内容：

${text}

润色说明：保持了原文的核心意思，同时优化了表达方式，使语言更加流畅自然，增强了文字的感染力和可读性。

优化要点：
1. 语言表达更加精准
2. 句式结构更加优美
3. 整体节奏更加和谐
4. 文化内涵得到更好体现`,

  'correct': (text: string) => `修正后的内容：

${text}

修正说明：检查了语法、拼写和语义，确保表达准确无误。原文整体质量较高，仅做了细微调整以提升准确性。

检查项目：
✓ 语法结构正确
✓ 用词准确恰当
✓ 语义表达清晰
✓ 标点符号规范`
};

/**
 * Mock AI 服务类
 */
export class MockAIService extends AIService {
  /**
   * 模拟流式处理
   */
  async processText(
    request: AIRequest,
    onStream: AIStreamCallback,
    onError?: (error: Error) => void
  ): Promise<void> {
    try {
      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 500));

      const mockResponse = this.generateMockResponse(request.action, request.text);
      
      // 模拟流式响应
      let currentIndex = 0;
      const streamChunks = this.splitIntoChunks(mockResponse);
      
      for (const chunk of streamChunks) {
        // 模拟每个块的延迟
        await new Promise(resolve => setTimeout(resolve, 100 + Math.random() * 200));
        
        onStream({
          content: chunk,
          isComplete: false
        });
      }

      // 完成处理
      onStream({
        content: '',
        isComplete: true
      });

    } catch (error) {
      console.error('Mock AI service error:', error);
      if (onError) {
        onError(error as Error);
      } else {
        onStream({
          content: '',
          isComplete: true,
          error: (error as Error).message
        });
      }
    }
  }

  /**
   * 生成 mock 响应
   */
  private generateMockResponse(action: string, text: string): string {
    const generator = MOCK_RESPONSES[action];
    if (!generator) {
      return `未知操作类型: ${action}`;
    }
    return generator(text);
  }

  /**
   * 将响应分割成流式块
   */
  private splitIntoChunks(text: string): string[] {
    const chunks: string[] = [];
    let currentIndex = 0;
    
    while (currentIndex < text.length) {
      const chunkSize = Math.floor(Math.random() * 15) + 5; // 5-20 字符的随机块
      const chunk = text.slice(currentIndex, currentIndex + chunkSize);
      chunks.push(chunk);
      currentIndex += chunkSize;
    }
    
    return chunks;
  }
}

/**
 * Mock AI 服务实例
 */
export const mockAIService = new MockAIService();
